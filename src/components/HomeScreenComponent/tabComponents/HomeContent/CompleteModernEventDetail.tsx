import React, {useState, useRef, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Alert,
  Platform,
  TextInput,
  Pressable,
  Linking,
  Image,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  interpolate,
  Extrapolate,
  FadeInDown,
  FadeInUp,
  SlideInRight,
} from 'react-native-reanimated';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useNavigation} from '@react-navigation/native';
import moment from 'moment-timezone';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import MapView, {Marker, PROVIDER_GOOGLE} from 'react-native-maps';
import {BottomSheetModalProvider, BottomSheetModal} from '@gorhom/bottom-sheet';
import {Dialog} from 'react-native-simple-dialogs';
import Hyperlink from 'react-native-hyperlink';
import {BlurView} from '@react-native-community/blur';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {Notifier, NotifierComponents} from 'react-native-notifier';
import firestore from '@react-native-firebase/firestore';
import Share from 'react-native-share';
import auth from '@react-native-firebase/auth';

import {Event} from '~types/api/event';
import {useTheme} from '~contexts/ThemeContext';
import {spacing, typography, borderRadius} from '~constants';
import {haptics} from '~utils/haptics';
import {useTranslation} from 'react-i18next';
import LikeButton from '~components/LikeButton';
import {NavigationProps} from '~types/navigation/navigation.type';
import {SCREENS} from '~constants';
import useTabBar from '~containers/Core/navigation/AppScreens/zustand';
import {useGetEventAttendees} from '~hooks/event/useGetEventAttendees';
import {useGetEventUserStatus} from '~hooks/event/useGetEventUserStatus';
import {useAddUserEventStatus} from '~hooks/event/useAddUserEventStatus';
import {useDeleteUserEventStatus} from '~hooks/event/useDeleteUserEventStatus';
import {useGetUserAccount} from '~hooks/user/useGetUser';
import {useGetBusinessAccount} from '~hooks/business/useGetBusinessAccount';
import {useGetEventMatchingStatus} from '~hooks/event/useGetEventMatchingStatus';
import {useGetCommentByEvent} from '~hooks/event/useGetCommentByEvent';
import {useCreateComment} from '~hooks/event/useCreateComment';
import {useDeleteEvent} from '~hooks/event/useDeleteEvent';
import {useCancelEvent} from '~hooks/event/useCancelEvent';
import {useUpdateUserPostCode} from '~hooks/user/useUpdateUserPostcode';
import useMatchingLoadingModalAnimation from '~hooks/react-hooks/useMatchingLoadingModalAnimation';
import {SUBSCRIPTION_STATUS, USER_EVENT_STATUS, User} from '~types/api/user';
import {Business} from '~types/api/business';
import {openLocationInMaps} from '~Utils/openLocationInMaps';
import ModernSpinner from '~components/ModernSpinner';
import Button from '~components/Button';
import CommentSheet from '~components/CommentSheet';
import Calendar from '~components/Calendar';
import QRCodeGenerator from '~components/QR-codes/QRCodeGenerator';
import ScannerQRCode from '~components/QR-codes/ScannerQRCode';
import AnimatedLoadingModal from '~components/Matching/AnimatedLoadingModal';
import IssueModal from '~types/components/issueModal';
import {getMapStyle} from '~constants/locationMap';
import {getMessageOfRecurrence} from '~Utils/event';
import FirebaseChatsService from '~services/FirebaseChats';
import {ChatType} from '~types/chat';
import {BigCheckIcon, BigPendingIcon, CheckIcon} from '~assets/icons';
import {useEventCreationStore} from '~providers/eventCreation/zustand';
import {useGetEventSubcategories} from '~hooks/event/useGetEventSubcategories';
import {useQuery} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import Config from 'react-native-config';
import axios from 'axios';
import {t} from 'i18next';

const {width: SCREEN_WIDTH, height: SCREEN_HEIGHT} = Dimensions.get('window');
const HEADER_HEIGHT = SCREEN_HEIGHT * 0.4; // Reduced from 50% to 35%
const TRUNCATE_DESCRIPTION_NUMBER_OF_LINES = 6;
const DATE_FORMAT = 'ddd MMM DD, YYYY';

// Helper function for random items
const getRandomItem = (arr: Array<any>) => {
  return arr[Math.floor(Math.random() * arr.length)];
};

// Random user arrays for fake attendees
const randomUserArray1 = [
  require('../../../../assets/images/user1.png'),
  require('../../../../assets/images/user2.png'),
  require('../../../../assets/images/user3.png'),
  require('../../../../assets/images/user4.png'),
  require('../../../../assets/images/user5.png'),
  require('../../../../assets/images/user6.png'),
  require('../../../../assets/images/user7.png'),
  require('../../../../assets/images/user8.png'),
];

const randomUserArray2 = [
  require('../../../../assets/images/user9.png'),
  require('../../../../assets/images/user10.png'),
  require('../../../../assets/images/user11.png'),
  require('../../../../assets/images/user12.png'),
  require('../../../../assets/images/user13.png'),
  require('../../../../assets/images/user14.png'),
  require('../../../../assets/images/user15.png'),
];

interface CompleteModernEventDetailProps {
  event: Event;
  onClose: () => void;
}

const CompleteModernEventDetail: React.FC<CompleteModernEventDetailProps> = ({event, onClose}) => {
  const {colors, isDarkMode} = useTheme();
  const {top} = useSafeAreaInsets();
  const navigation = useNavigation<NavigationProps>();
  const {setIsTabBarDisabled} = useTabBar();
  const {t} = useTranslation();
  const scrollY = useSharedValue(0);

  // State management
  const [descriptionNumberOfLines, setDescriptionNumberOfLines] = useState(TRUNCATE_DESCRIPTION_NUMBER_OF_LINES);
  const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(true); // Default expanded like your previous design
  const [lengthMore, setLengthMore] = useState(false);
  const [modalIsVisible, setModalIsVisible] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [spinner, setIsSpinner] = useState(false);
  const [isAnimationVisible, setAnimationVisisble] = useState(false);
  const [eventCountry, setEventCountry] = useState('');
  const [pincodeDialogVisible, setPinCodeDialog] = useState(false);
  const [postCode, setPostCode] = useState('');
  const [randomUserImg1, setRandomUserImg1] = useState<any>();

  // Set event country on mount and initialize fake attendees
  useEffect(() => {
    // Initialize random user image for fake attendee
    setRandomUserImg1(getRandomItem(randomUserArray1));
  }, []);

  // Refs
  const calendarRef = useRef<{open: () => void; close: () => void}>();
  const matchingOptionsSheetRef = useRef<BottomSheetModal>(null);
  const commentSheetRef = useRef<BottomSheetModal>(null);
  const mapRef = useRef<MapView>(null);

  // API Hooks - exactly like old implementation
  const {data: pyxiHost} = useGetBusinessAccount('wLJLEn8J6oN9RpPyep2BjdnagcA2');
  const {
    data: eventAttendees,
    refetch: refetchAttendees,
    isLoading: attendeesLoading,
  } = useGetEventAttendees({
    event_id: event.event_id,
    subscription_status: SUBSCRIPTION_STATUS.ACCEPTED,
  });
  const {data: matchingStatus, refetch: refetchMatchinEventSatus} = useGetEventMatchingStatus(event.event_id + '');
  const {
    data: commentList,
    refetch: refetchCommentEvent,
    isLoading: isCommentEventLoading,
  } = useGetCommentByEvent(event.event_id);
  const {mutateAsync: createComment} = useCreateComment();
  const {
    data: userStatus,
    isLoading: userStatusIsLoading,
    isFetching: userStatusIsFetching,
    refetch,
  } = useGetEventUserStatus({event_id: event.event_id, user_id: auth()!.currentUser?.uid || ''});
  const {mutateAsync: addEventUserStatus} = useAddUserEventStatus();
  const {data: userAccount, refetch: refetchUser} = useGetUserAccount(auth().currentUser?.uid);
  const {data: businessAccount, refetch: refetchBusiness} = useGetBusinessAccount(auth().currentUser?.uid || '');
  const {data: hostUser} = useGetUserAccount(event?.host_id || '');
  const {data: hostBusiness} = useGetBusinessAccount(event?.host_id || '');
  const {mutateAsync: deleteEvent} = useDeleteEvent();
  const {mutateAsync: cancelAEvent} = useCancelEvent();
  const {mutateAsync: deleteEventUserStatus} = useDeleteUserEventStatus();
  const {mutateAsync: updateUserMutation} = useUpdateUserPostCode();
  const {data: categoryData} = useGetEventSubcategories(event.event_id);
  const {setSubCategory} = useEventCreationStore();

  // Matching hooks
  const {openMatchingLoadingModal, setCurrentMatchingEvent, setDomain, setRefresh, setMatchingType, matchingType} =
    useMatchingLoadingModalAnimation();

  // Computed values
  const isUserIsAttendee = eventAttendees?.some(attendee => attendee.user?.uid === userAccount?.uid) || false;
  const user = !(userAccount as unknown as {detail: string})?.detail ? userAccount : businessAccount;
  const host = !(hostUser as unknown as {detail: string})?.detail ? hostUser : hostBusiness;
  const isUserEvent = event?.host_id === auth().currentUser?.uid;
  const isEventPassed = moment(event?.end_date).isBefore(moment());

  const publicDomains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com'];
  const userDomain = user?.email?.split('@')[1];
  const isPublicDomain = publicDomains.includes(userDomain || '');

  // Groups query for matching
  const {data: groups} = useQuery<any[]>('joined-groups', async () => {
    const token = await FirebaseAuth.getAuthToken();
    const config = {
      headers: {Authorization: token, Accept: 'application/json'},
    };
    const response = await axios.get(Config.BASE_API_URL + 'community/joined', config);
    return response.data;
  });

  const styles = createStyles(colors);

  // Animated styles
  const headerAnimatedStyle = useAnimatedStyle(() => {
    const opacity = interpolate(scrollY.value, [0, HEADER_HEIGHT - 100], [1, 0], Extrapolate.CLAMP);
    const scale = interpolate(scrollY.value, [0, HEADER_HEIGHT], [1, 1.1], Extrapolate.CLAMP);
    return {
      opacity,
      transform: [{scale}],
    };
  });

  const overlayAnimatedStyle = useAnimatedStyle(() => {
    const opacity = interpolate(scrollY.value, [0, HEADER_HEIGHT - 100], [0, 1], Extrapolate.CLAMP);
    return {
      opacity,
    };
  });

  // Event handlers
  const handleSharePress = async () => {
    if (!event?.event_id) {
      return;
    }
    const userName = (user as User)?.last_name
      ? (user as User)?.first_name + ' ' + (user as User)?.last_name
      : (user as User)?.first_name || (user as Business)?.name;
    const redirectLink = `https://partner.pyxi.ai/event/detail/${event.event_id}`;
    const url = redirectLink;
    await Share.open({
      message: t('invite_message', {userName, itemName: event.name, url}),
    });
  };

  const handleOpenMaps = () => {
    if (!event?.coords?.lat || !event?.coords?.long) {
      return;
    }
    openLocationInMaps({lat: event.coords.lat, long: event.coords.long, pointLabel: event?.address_name});
  };

  // Join/Leave Event Logic - exactly like old implementation
  const onNotificationAboutSubscriptionOn = async (item: any, host: any, responseData: any) => {
    setTimeout(async () => {
      if (responseData.status === USER_EVENT_STATUS.PENDING) {
        Notifier.showNotification({
          title: 'Please wait for the host to accept your request.',
          Component: NotifierComponents.Alert,
          componentProps: {
            alertType: 'info',
          },
        });
        return;
      } else if (responseData.status === 'waiting') {
        Notifier.showNotification({
          title: 'You are in waitlist. Please wait for the host to accept your request.',
          Component: NotifierComponents.Alert,
          componentProps: {
            alertType: 'info',
          },
        });
      } else {
        Notifier.showNotification({
          title: 'All Set! You are Ready for the event.',
          Component: NotifierComponents.Alert,
          componentProps: {
            alertType: 'success',
          },
        });
        refetchAttendees();
        if (item?.host_id != 'wLJLEn8J6oN9RpPyep2BjdnagcA2') {
          setAnimationVisisble(true);
        }
      }
    }, 2000);
  };

  const onNotificationAboutSubscriptionOff = async (item: any, host: any) => {
    setTimeout(async () => {
      Notifier.showNotification({
        title: 'We notified the organiser you are no longer going to the event.',
        Component: NotifierComponents.Alert,
        componentProps: {
          alertType: 'success',
        },
      });
      try {
        const eventOwnerDoc = await firestore().collection('users').doc(host.uid).get();
        const eventOwnerData = eventOwnerDoc.data();
        if (eventOwnerData && eventOwnerData.deviceFcmToken) {
          await FirebaseChatsService.sendPushNotification(
            eventOwnerData.deviceFcmToken,
            'New Event Notification',
            `User ${userAccount?.first_name} are no longer going to the event ${item?.name}`,
            {
              clickAction: 'OPEN_NOTIFICATIONS',
              eventId: item.event_id,
              item: JSON.stringify(item),
            },
          );
        }
      } catch (error) {
        console.error('Error fetching event owner data:', error);
      }
    }, 2000);
  };

  const joinEvent = async (event_id?: number, isLeave?: boolean) => {
    if (!user?.onboarding_answers || user.onboarding_answers.length === 0) {
      navigation.navigate(SCREENS.EDIT_PREFERANCE, {setting: true});
      return;
    }
    if (event?.event_group_id && !event_id) {
      calendarRef.current?.open();
      return;
    }
    if (event && !event?.payment_url) {
      if (userStatus?.status !== USER_EVENT_STATUS.ACCEPTED) {
        navigation.navigate(SCREENS.BUY_TICKET, {eventId: event.event_id});
        return;
      } else {
        navigation.navigate(SCREENS.PAYMENT_SUCCESS, {order_id: userStatus.order_id + ''});
        return;
      }
    }

    if (event?.payment_url && userStatus?.status !== USER_EVENT_STATUS.ACCEPTED) {
      setModalIsVisible(true);
    }

    if (!event) {
      return;
    }
    if (event_id) {
      if (!isLeave) {
        const data = await addEventUserStatus({
          event_id: event_id.toString(),
        });
        if (!event?.is_paid) {
          userStatus?.status === USER_EVENT_STATUS.ACCEPTED
            ? onNotificationAboutSubscriptionOff(event, host)
            : onNotificationAboutSubscriptionOn(event, host, data);
        }

        if (event.event_type === 'business' || event.private) {
          await FirebaseChatsService.addNewUserToTheGroupChat({
            user_id: auth().currentUser!.uid,
            user_name: `${(user as User)?.first_name || ''} ${(user as User)?.last_name || ''}`,
            user_image: user!.photo,
            event_id: event_id,
          });
        }
      } else {
        await deleteEventUserStatus({event_id: event_id, user_id: auth().currentUser!.uid});
        await FirebaseChatsService.removeUserFromTheGroupChat({
          user_id: auth().currentUser!.uid,
          user_name: `${(user as User)!.first_name || ''} ${(user as User)!.last_name || ''}`,
          event_id: event_id,
          event_image: event.image_url,
        });
      }
    } else {
      if (!userStatus) {
        const data = await addEventUserStatus({
          event_id: event.event_id.toString(),
        });
        if (!event?.is_paid) {
          userStatus?.status === USER_EVENT_STATUS.ACCEPTED
            ? onNotificationAboutSubscriptionOff(event, host)
            : onNotificationAboutSubscriptionOn(event, host, data);
        }

        if (event.event_type === 'business' || event.private) {
          await FirebaseChatsService.addNewUserToTheGroupChat({
            user_id: auth().currentUser!.uid,
            user_name: `${(user as User)?.first_name || ''} ${(user as User)?.last_name || ''}`,
            user_image: user!.photo,
            event_id: event.event_id,
          });
        }
      } else {
        await deleteEventUserStatus({event_id: event.event_id, user_id: auth().currentUser!.uid});
        await FirebaseChatsService.removeUserFromTheGroupChat({
          user_id: auth().currentUser!.uid,
          user_name: `${(user as User)!.first_name || ''} ${(user as User)!.last_name || ''}`,
          event_id: event.event_id,
          event_image: event.image_url,
        });
      }
    }
    if (event_id) {
      if (event_id !== event.event_id) {
        navigation.replace(SCREENS.HOME_EVENT, {eventId: event_id});
      } else {
        await refetch();
      }
    } else {
      await refetch();
    }
  };

  const onJoinEventClick = () => {
    if (!userStatus && eventCountry === 'Greece') {
      if (userAccount?.postal_code) {
        joinEvent();
      } else {
        setPinCodeDialog(true);
      }
    } else {
      joinEvent();
    }
  };

  // Add to Calendar functionality - exactly like old implementation
  const addEventToCalendar = async (title: any, startDate: any, endDate: any, description: any, location: any) => {
    const now = new Date();
    const startDateEvent = new Date(startDate);
    const effectiveStartDate = startDateEvent < now ? now : startDateEvent;

    try {
      const RNCalendarEvents = require('react-native-calendar-events');
      const authStatus = await RNCalendarEvents.requestPermissions();
      if (authStatus === 'authorized') {
        const eventId = await RNCalendarEvents.saveEvent(title, {
          startDate: effectiveStartDate.toISOString(),
          endDate: new Date(endDate).toISOString(),
          description: description,
          location: location,
        });

        if (Platform.OS === 'ios') {
          const AddCalendarEvent = require('react-native-add-calendar-event');
          AddCalendarEvent.presentEventViewingDialog({eventId: eventId})
            .then((event: any) => {
              Alert.alert('Calendar', 'Your event has been saved.');
            })
            .catch((error: any) => {
              console.error('Error presenting event viewing dialog', error);
              Alert.alert('Error', 'Failed to open the event viewer: ' + error.message);
            });
        } else {
          Alert.alert('Unsupported Feature', 'This feature is only available on iOS.');
        }
      } else {
        console.error('Calendar permission is not authorized');
        return;
      }
    } catch (error) {
      console.error('Failed to add event to calendar:', error);
    }
  };

  const addCalendarEvent = async (eventConfig: any) => {
    try {
      const AddCalendarEvent = require('react-native-add-calendar-event');
      const eventInfo = await AddCalendarEvent.presentEventCreatingDialog(eventConfig);
      setTimeout(() => {
        if (eventInfo?.action !== 'CANCELED') {
          Alert.alert('Calendar', 'Your event has been saved.');
        } else {
          Alert.alert('Calendar', 'Your event has been cancelled.');
        }
      }, 2000);
    } catch (error) {
      Alert.alert('Error', 'Failed to add event to calendar');
      console.error('Add event error:', error);
    }
  };

  const addToCalendar = () => {
    if (Platform.OS == 'ios') {
      addEventToCalendar(event?.name, event?.start_date, event?.end_date, event?.description, event?.address_name);
    } else {
      const eventConfig = {
        title: event?.name,
        startDate: new Date(event?.start_date || '').toISOString(),
        endDate: new Date(event?.end_date || '').toISOString(),
        location: event?.address_name,
        notes: event?.description,
      };

      addCalendarEvent(eventConfig);
    }
  };

  // Host Chat functionality - exactly like old implementation
  const onHostChatClick = async (host: Business) => {
    setModalVisible(true);
  };

  const onIssueTypeClick = async (type: string) => {
    const hHost = type == 't' ? pyxiHost : host;
    if (hHost && event) {
      const chatId = await FirebaseChatsService.createOrganisationChat({
        user_id1: auth().currentUser!.uid,
        user_id2: hHost?.uid,
        user_name1: `${userAccount?.first_name} ${userAccount?.last_name || ''}`,
        user_name2: (hHost as Business)?.name
          ? (hHost as Business)?.name
          : (hHost as unknown as User)?.last_name
            ? `${(hHost as unknown as User)?.first_name} ${(hHost as unknown as User)?.last_name || ''}`
            : (hHost as unknown as User)?.first_name || '',
        user_image: userAccount?.photo + '',
        event: event,
        isTechnical: type == 't' ? true : false,
      });

      const chatRef = firestore().collection('chats').doc(chatId);
      const doc = await chatRef.get();
      if (doc.exists) {
        const chatData = doc.data() as ChatType;
        const updatedMessages = chatData.history.map((message: any) => {
          if (!message.readUserIds?.includes(auth().currentUser!.uid)) {
            console.log('Updating message:', message);
            return {...message, readUserIds: [...(message.readUserIds || []), auth().currentUser!.uid]};
          }
          return message;
        });

        await chatRef.update({history: updatedMessages});
      }

      navigation.navigate(SCREENS.CHAT_STACK, {key: chatId});
    }
  };

  // Comments functionality - exactly like old implementation
  const onCommentPress = () => {
    commentSheetRef.current?.present();
  };

  const onAddComment = async (comment: string) => {
    const reqBody = {
      comment: comment,
      pin: false,
      parent_id: null,
      event_id: event?.event_id || 0,
    };
    await createComment(reqBody);
    refetchCommentEvent();
  };

  // Event Management Functions - exactly like old implementation
  const handleEditEvent = () => {
    if (categoryData && categoryData.length > 0) {
      setSubCategory(categoryData[0]);
    }
    navigation.navigate(SCREENS.CREATE_EVENT, {
      eventId: event?.event_id,
      isEdit: true,
    });
  };

  const cancelEvent = async () => {
    setIsLoading(true);
    try {
      await cancelAEvent({event_id: event?.event_id || 0});
      Alert.alert('Success', 'Event has been cancelled successfully');
      navigation.goBack();
    } catch (error) {
      Alert.alert('Error', 'Failed to cancel event');
    } finally {
      setIsLoading(false);
    }
  };

  const deleteEventHandler = async () => {
    Alert.alert('Delete Event', 'Are you sure you want to delete this event? This action cannot be undone.', [
      {
        text: 'Cancel',
        style: 'cancel',
      },
      {
        text: 'Delete',
        style: 'destructive',
        onPress: async () => {
          setIsLoading(true);
          try {
            await deleteEvent({event_id: event?.event_id || 0});
            Alert.alert('Success', 'Event has been deleted successfully');
            navigation.goBack();
          } catch (error) {
            Alert.alert('Error', 'Failed to delete event');
          } finally {
            setIsLoading(false);
          }
        },
      },
    ]);
  };

  // Postal Code Dialog for Greek Events - exactly like old implementation
  const onContinuePress = async () => {
    if (postCode.trim()) {
      try {
        await updateUserMutation({postal_code: postCode});
        setPinCodeDialog(false);
        setPostCode('');
        joinEvent();
      } catch (error) {
        Alert.alert('Error', 'Failed to update postal code');
      }
    } else {
      Alert.alert('Error', 'Please enter a valid postal code');
    }
  };

  // Helper functions for event type
  const getEventTypeColor = () => {
    switch (event?.event_type) {
      case 'business':
        return colors.statusPurple;
      case 'private':
        return colors.statusOrange;
      default:
        return colors.primary;
    }
  };

  const getEventTypeLabel = () => {
    switch (event?.event_type) {
      case 'business':
        return 'Business Event';
      case 'private':
        return 'Private Event';
      default:
        return 'Public Event';
    }
  };

  // Find People to Go With handler - THE MOST PROMINENT FEATURE
  const handleMatchingPress = () => {
    if (false && userStatus?.status != USER_EVENT_STATUS.ACCEPTED) {
      Alert.alert('Note!', 'Please join the event before searching for someone to go with.');
      return;
    }
    if (matchingStatus?.matches_available && matchingStatus?.type === 'colleague') {
      Alert.alert(
        'Note!',
        "If you find someone to go with, your current colleague matches will be reset. However, you'll be able to find them again later. Are you sure you want to proceed?",
        [
          {
            text: 'No',
            onPress: () => {
              // If user cancels, close the dialog and do nothing
            },
            style: 'cancel',
          },
          {
            text: 'Yes',
            onPress: () => {
              setCurrentMatchingEvent(event?.event_id || null);
              setRefresh(true);
              openMatchingLoadingModal();
            },
          },
        ],
      );
    } else {
      // Check if user has groups before showing bottom sheet
      if (groups && groups.length > 0) {
        matchingOptionsSheetRef.current?.present();
      } else {
        // If no groups, go directly to matching with 'anyone' type
        setCurrentMatchingEvent(event?.event_id || null);
        setDomain(userDomain || null);
        setMatchingType('anyone');
        setRefresh(false);
        openMatchingLoadingModal();
      }
    }
  };

  return (
    <BottomSheetModalProvider>
      <View style={styles.container}>
        {/* Header Image - Like your previous beautiful design */}
        <Animated.View style={[styles.headerContainer, headerAnimatedStyle]}>
          <FastImage source={{uri: event.image_url}} style={styles.headerImage} resizeMode="cover" />
          <View style={styles.headerOverlay} />
          {/* Header Close button */}
          <View style={[styles.headerActions, {top: top + 10}]}>
            <TouchableOpacity
              onPress={() => {
                console.log('🔄 Close button pressed!');
                onClose();
              }}
              style={styles.actionButton}
              activeOpacity={0.8}>
              <Ionicons name="close" size={24} color={colors.white} />
            </TouchableOpacity>
          </View>

          {/* Event Type Badge */}
          <Animated.View
            style={[styles.eventTypeBadge, {backgroundColor: getEventTypeColor()}]}
            entering={FadeInDown.delay(200)}>
            <Text style={styles.eventTypeBadgeText}>{getEventTypeLabel()}</Text>
          </Animated.View>
        </Animated.View>

        {/* Sticky Header Overlay - With back button */}
        <Animated.View style={[styles.stickyHeader, overlayAnimatedStyle, {top}]}>
          <TouchableOpacity
            onPress={() => {
              console.log('🔄 Sticky header back button pressed!');
              onClose();
            }}
            style={styles.stickyBackButton}
            activeOpacity={0.8}>
            <Ionicons name="arrow-back" size={24} color={colors.textPrimary} />
          </TouchableOpacity>
          <Text style={styles.stickyTitle} numberOfLines={1}>
            {event.name}
          </Text>
          <View style={styles.stickyPlaceholder} />
        </Animated.View>

        {/* Scrollable Content - Positioned absolutely from top */}
        <Animated.ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          onScroll={e => {
            scrollY.value = e.nativeEvent.contentOffset.y;
          }}
          scrollEventThrottle={16}
          showsVerticalScrollIndicator={false}>
          {/* Spacer to account for header image */}
          <View style={styles.headerSpacer}>
            {/* Share and Like buttons - Bottom right corner of header area */}
            <View style={styles.scrollableBottomRightActions}>
              <TouchableOpacity
                onPress={() => {
                  console.log('🔄 Share button pressed!');
                  handleSharePress();
                }}
                style={styles.actionButton}
                activeOpacity={0.8}>
                <Ionicons name="share-outline" size={24} color={colors.white} />
              </TouchableOpacity>
              <View style={styles.actionButton}>
                <LikeButton liked={event.user_liked} eventId={event.event_id} />
              </View>
            </View>
          </View>

          {/* Main Content Card */}
          <Animated.View style={styles.contentCard} entering={FadeInUp.delay(100)}>
            {/* Event Title */}
            <Text style={styles.eventTitle}>{event.name}</Text>

            {/* Event Info Grid */}
            <View style={styles.eventInfoGrid}>
              {/* Date & Time */}
              <View style={styles.infoItem}>
                <View style={styles.infoIcon}>
                  <MaterialIcons name="event" size={24} color={colors.primary} />
                </View>
                <View style={styles.infoContent}>
                  <Text style={styles.infoLabel}>Date & Time</Text>
                  <Text style={styles.infoValue}>
                    {event?.start_date ? moment.utc(event?.start_date).format('ddd, MMM DD, YYYY') : 'N/A'}
                  </Text>
                  <Text style={styles.infoSubValue}>
                    {event?.start_date ? moment.utc(event?.start_date).format('h:mm A') : ''}
                    {event?.end_date ? ` - ${moment.utc(event?.end_date).format('h:mm A')}` : ''}
                  </Text>
                </View>
                <TouchableOpacity onPress={() => addToCalendar()} style={styles.calendarIconButton}>
                  <MaterialIcons name="edit-calendar" size={24} color={colors.primary} />
                </TouchableOpacity>
              </View>

              {/* Location */}
              <TouchableOpacity style={styles.infoItem} onPress={handleOpenMaps}>
                <View style={styles.infoIcon}>
                  <MaterialIcons name="location-on" size={24} color={colors.primary} />
                </View>
                <View style={styles.infoContent}>
                  <Text style={styles.infoLabel}>Location</Text>
                  <Text style={styles.infoValue}>{event.address_name}</Text>
                  <Text style={[styles.infoSubValue, {color: colors.primary}]}>Tap for directions</Text>
                </View>
              </TouchableOpacity>

              {/* Attendees */}
              <View style={styles.infoItem}>
                <View style={styles.infoIcon}>
                  <MaterialIcons name="people" size={24} color={colors.primary} />
                </View>
                <View style={styles.infoContent}>
                  <Text style={styles.infoLabel}>Attendees</Text>
                  <Text style={styles.infoValue}>{event.total_attendee_count || 0} people attending</Text>
                  {eventAttendees && eventAttendees.length > 0 && (
                    <View style={styles.attendeeAvatarsRow}>
                      {eventAttendees.slice(0, 3).map((attendee, index) => (
                        <View
                          key={attendee.user_id}
                          style={[styles.attendeeAvatarSmall, {marginLeft: index > 0 ? -8 : 0}]}>
                          {attendee.user?.photo ? (
                            <FastImage
                              source={{uri: attendee.user.photo}}
                              style={styles.attendeeAvatarImage}
                              resizeMode="cover"
                            />
                          ) : (
                            <View style={[styles.attendeeAvatarImage, styles.attendeePlaceholder]}>
                              <Text style={styles.attendeeInitial}>
                                {(attendee.user?.first_name || attendee.name || 'U').charAt(0).toUpperCase()}
                              </Text>
                            </View>
                          )}
                        </View>
                      ))}
                      {eventAttendees.length > 3 && (
                        <Text style={styles.moreAttendeesText}>+{eventAttendees.length - 3}</Text>
                      )}
                    </View>
                  )}
                </View>
              </View>

              {/* Price (if paid event) */}
              {event.is_paid && (
                <View style={styles.infoItem}>
                  <View style={styles.infoIcon}>
                    <MaterialIcons name="attach-money" size={24} color={colors.primary} />
                  </View>
                  <View style={styles.infoContent}>
                    <Text style={styles.infoLabel}>Price</Text>
                    <Text style={styles.infoValue}>Paid Event</Text>
                    <Text style={styles.infoSubValue}>Tickets required</Text>
                  </View>
                </View>
              )}

              {/* Recurrence (if recurring event) */}
              {event?.event_group_id && (
                <View style={styles.infoItem}>
                  <View style={styles.infoIcon}>
                    <MaterialIcons name="repeat" size={24} color={colors.primary} />
                  </View>
                  <View style={styles.infoContent}>
                    <Text style={styles.infoLabel}>Recurrence</Text>
                    <Text style={styles.infoValue}>{getMessageOfRecurrence(event?.event_group)}</Text>
                  </View>
                </View>
              )}
            </View>

            {/* Event Description */}
            <View style={styles.descriptionSection}>
              <Text style={styles.sectionTitle}>About this event</Text>
              <Text style={styles.eventDescription} numberOfLines={descriptionNumberOfLines}>
                {event.description || 'No description available.'}
              </Text>
              {event.description && event.description.length > 300 && (
                <TouchableOpacity
                  onPress={() => {
                    setDescriptionNumberOfLines(
                      descriptionNumberOfLines === TRUNCATE_DESCRIPTION_NUMBER_OF_LINES
                        ? undefined
                        : TRUNCATE_DESCRIPTION_NUMBER_OF_LINES,
                    );
                  }}
                  style={styles.expandButton}>
                  <Text style={[styles.expandText, {color: colors.primary}]}>
                    {descriptionNumberOfLines === TRUNCATE_DESCRIPTION_NUMBER_OF_LINES
                      ? 'Read more...'
                      : 'Read less...'}
                  </Text>
                </TouchableOpacity>
              )}
            </View>

            {/* COMMENTS/UPDATES SECTION */}
            {((commentList && commentList?.length > 0) || isUserEvent) && (
              <View style={styles.commentsSection}>
                {commentList && commentList?.length > 0 ? (
                  <TouchableOpacity onPress={onCommentPress} style={styles.commentView}>
                    <Text style={styles.commentTitle}>Updates {commentList.length}</Text>
                    <View style={styles.commentItemView}>
                      <FastImage source={{uri: commentList[0].user.photo}} style={styles.userImage} />
                      <Text style={styles.commentTextView}>{commentList[commentList.length - 1].comment}</Text>
                    </View>
                  </TouchableOpacity>
                ) : (
                  isUserEvent && (
                    <TouchableOpacity onPress={onCommentPress} style={styles.commentView}>
                      <Text style={styles.commentTitle}>Updates</Text>
                      <View style={[styles.commentItemView, {alignItems: 'center'}]}>
                        <FastImage source={{uri: user?.photo}} style={styles.userImage} />
                        <Text style={styles.commentTextView}>Add updates here</Text>
                      </View>
                    </TouchableOpacity>
                  )
                )}
              </View>
            )}

            {/* JOIN/LEAVE EVENT BUTTON */}
            {event?.host_id !== auth().currentUser?.uid && !userStatusIsLoading && !userStatusIsFetching ? (
              <View style={styles.joinSection}>
                {!isEventPassed || userStatus?.status ? (
                  <Button
                    disabled={
                      userStatus?.status === USER_EVENT_STATUS.PENDING ||
                      event?.is_cancelled ||
                      moment(event?.end_date).isBefore(moment())
                    }
                    icon={
                      userStatus?.status === USER_EVENT_STATUS.ACCEPTED ? (
                        <BigCheckIcon />
                      ) : userStatus?.status === USER_EVENT_STATUS.PENDING ? (
                        <BigPendingIcon />
                      ) : undefined
                    }
                    label={
                      userStatus?.status === USER_EVENT_STATUS.ACCEPTED
                        ? 'Accepted'
                        : userStatus?.status === USER_EVENT_STATUS.PENDING
                          ? 'Pending'
                          : userStatus?.status === 'waiting'
                            ? 'Waitlisted'
                            : event?.is_paid || event?.payment_url
                              ? 'Buy tickets'
                              : 'Join'
                    }
                    containerStyle={{
                      backgroundColor: event?.is_cancelled
                        ? colors.statusGray
                        : userStatus?.status === USER_EVENT_STATUS.ACCEPTED
                          ? colors.statusGreen
                          : userStatus?.status === USER_EVENT_STATUS.PENDING
                            ? colors.primary
                            : userStatus?.status === 'waiting'
                              ? colors.primary
                              : colors.statusPurple,
                      marginTop: 20,
                      borderRadius: 20,
                    }}
                    textStyle={{
                      fontSize: userStatus?.status === 'waiting' ? 12 : 16,
                      fontWeight: '600',
                      textAlign: 'center',
                      color: colors.white,
                      lineHeight: 20,
                    }}
                    onPress={async () => onJoinEventClick()}
                  />
                ) : (
                  <Button
                    label={'Buy tickets'}
                    containerStyle={{
                      backgroundColor: colors.gray400,
                      marginTop: 20,
                      borderRadius: 20,
                    }}
                    textStyle={{
                      fontSize: 14,
                      fontWeight: '600',
                      textAlign: 'center',
                      color: colors.white,
                      lineHeight: 20,
                    }}
                    onPress={async () => Alert.alert('The event has ended')}
                  />
                )}
              </View>
            ) : (
              (userStatusIsLoading || userStatusIsFetching) && (
                <View style={styles.loaderJoinBtnView}>
                  <ModernSpinner size={24} variant="circular" />
                </View>
              )
            )}

            {/* FIND PEOPLE TO GO WITH - MOST PROMINENT FEATURE */}
            {!isUserEvent && (
              <View style={styles.findPeopleSection}>
                <Button
                  label={
                    matchingStatus?.matches_available
                      ? matchingStatus?.type === 'all'
                        ? t('events.see_my_match')
                        : t('home.find_someone')
                      : t('home.find_someone')
                  }
                  containerStyle={{
                    backgroundColor: matchingStatus?.matches_available ? colors.statusGreen : colors.statusPurple,
                    marginTop: 20,
                  }}
                  textStyle={{fontSize: 16, fontWeight: '600', color: colors.white}}
                  onPress={handleMatchingPress}
                />

                {/* Find My Colleagues Button for business events */}
                {(host as Business) && (host as Business)!.user_pooling! && (
                  <Button
                    label={
                      matchingStatus?.matches_available
                        ? matchingStatus?.type === 'colleague'
                          ? 'See My Colleagues'
                          : 'Find My Colleagues'
                        : 'Find My Colleagues'
                    }
                    containerStyle={{
                      backgroundColor: colors.primary,
                      marginTop: 10,
                    }}
                    textStyle={{fontSize: 16, fontWeight: '600', color: colors.white}}
                    onPress={() => {
                      if (matchingStatus?.matches_available && matchingStatus?.type === 'all') {
                        Alert.alert(
                          'Note!',
                          "If you find my colleagues, your current matches will be reset. However, you'll be able to find them again later. Are you sure you want to proceed?",
                          [
                            {
                              text: 'No',
                              onPress: () => {},
                              style: 'cancel',
                            },
                            {
                              text: 'Yes',
                              onPress: () => {
                                setCurrentMatchingEvent(event?.event_id || null);
                                setRefresh(true);
                                openMatchingLoadingModal();
                              },
                            },
                          ],
                        );
                      } else {
                        matchingOptionsSheetRef.current?.present();
                      }
                    }}
                  />
                )}
              </View>
            )}

            {/* EVENT MANAGEMENT BUTTONS FOR OWNERS */}
            {isUserEvent ? (
              <View style={styles.eventManagementSection}>
                {event?.is_cancelled ? (
                  <Text style={styles.errorText}>
                    {event?.event_type === 'business'
                      ? 'This event was cancelled by the organiser'
                      : 'This event was cancelled by host.'}
                  </Text>
                ) : null}

                <Button
                  label={t('events.update_event')}
                  containerStyle={{backgroundColor: colors.primary, marginTop: 10}}
                  textStyle={{fontSize: 16, fontWeight: '600', color: colors.white}}
                  onPress={handleEditEvent}
                />

                <Button
                  label={t('events.cancel_event')}
                  containerStyle={{backgroundColor: colors.error, marginTop: 10}}
                  textStyle={{fontSize: 16, fontWeight: '600', color: colors.white}}
                  onPress={() => cancelEvent()}
                  isLoading={isLoading}
                />

                <Button
                  label="Delete Event"
                  containerStyle={{backgroundColor: colors.error, marginTop: 10}}
                  textStyle={{fontSize: 16, fontWeight: '600', color: colors.white}}
                  onPress={deleteEventHandler}
                  isLoading={isLoading}
                />
              </View>
            ) : null}

            {/* HOST INFORMATION SECTION */}
            {!!host && (
              <View style={styles.hostSection}>
                <View style={styles.sectionHeader}>
                  <Text style={styles.sectionTitle}>{t('events.host')}</Text>
                  {isUserEvent && (
                    <Button
                      onPress={() =>
                        navigation.navigate(SCREENS.PENDING_ATTENDEES, {
                          eventId: event!.event_id,
                          eventName: event!.name,
                        })
                      }
                      label={t('events.requiring_confirmation')}
                      containerStyle={{
                        backgroundColor: colors.statusPurple,
                        borderRadius: 50,
                      }}
                      textStyle={{fontSize: 15, lineHeight: 16, fontWeight: '500', color: colors.white}}
                    />
                  )}
                </View>
                <TouchableOpacity
                  style={styles.hostContainer}
                  onPress={() => {
                    navigation.navigate(SCREENS.PERSONAL_INFO, {
                      user: {
                        tag: host?.uid + 'image',
                        source: host?.photo || '',
                        description: host?.description || '',
                        name: (host as Business)?.name
                          ? (host as Business)?.name
                          : (host as User)?.last_name
                            ? `${(host as User)?.first_name} ${(host as User)?.last_name || ''}`
                            : (host as User)?.first_name || '',
                        user_id: host?.uid || '',
                        eventName: event?.name,
                      },
                    });
                  }}>
                  <View style={styles.hostInfo}>
                    <View style={styles.hostPhoto}>
                      <FastImage
                        style={StyleSheet.absoluteFillObject}
                        resizeMode={'cover'}
                        source={{uri: host?.photo, priority: 'normal'}}
                      />
                    </View>

                    <Text style={styles.hostName}>
                      {(host as Business)?.name
                        ? (host as Business)?.name
                        : (host as User)?.last_name
                          ? `${(host as User)?.first_name} ${(host as User)?.last_name || ''}`
                          : (host as User)?.first_name || ''}
                    </Text>
                    {!isUserEvent && (
                      <Button
                        onPress={() => onHostChatClick(host as Business)}
                        label={t('chat.chat')}
                        containerStyle={{
                          backgroundColor: colors.statusPurple,
                          borderRadius: 50,
                        }}
                        textStyle={{fontSize: 15, lineHeight: 16, fontWeight: '500', color: colors.white}}
                      />
                    )}
                  </View>
                </TouchableOpacity>
              </View>
            )}

            {/* ATTENDEES LIST SECTION */}
            <View style={styles.attendeesSection}>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>{t('events.attendees')}</Text>
              </View>
              <View style={styles.attendeesContainer}>
                {event?.host_id != user?.uid && (
                  <View style={styles.attendeeRowStyle}>
                    {eventAttendees && eventAttendees.length > 0 ? (
                      <>
                        <FastImage style={styles.profileImg} source={{uri: eventAttendees[0].user?.photo}} />
                        <Text style={styles.attendeesCount}>+ {Math.max(0, (eventAttendees.length || 0) - 1)}</Text>
                      </>
                    ) : (
                      // Show single fake attendee when no real attendees
                      <>
                        <Image style={styles.profileImg} source={randomUserImg1} />
                        <Text style={styles.attendeesCount}>+ 1</Text>
                      </>
                    )}
                  </View>
                )}
                {event?.host_id === user?.uid && (
                  <>
                    {eventAttendees ? (
                      eventAttendees.map?.((attendee, index) => (
                        <View key={index}>
                          <TouchableOpacity
                            style={styles.attendeeContainer}
                            disabled={!attendee.user}
                            onPress={() => {
                              navigation.navigate(SCREENS.PERSONAL_INFO, {
                                user: {
                                  tag: index + 'image',
                                  source: attendee.user.photo,
                                  description: attendee.user.description,
                                  name: `${attendee.user.first_name} ${attendee.user.last_name || ''}`,
                                  user_id: attendee.user.uid,
                                  eventName: event?.name,
                                },
                              });
                            }}>
                            <View style={styles.attendeeLeftContainer}>
                              {attendee?.user && (
                                <View style={styles.attendeePhoto}>
                                  <FastImage
                                    style={StyleSheet.absoluteFillObject}
                                    resizeMode={'cover'}
                                    source={{uri: attendee?.user?.photo, priority: 'normal'}}
                                  />
                                </View>
                              )}

                              {attendee?.user ? (
                                <Text
                                  style={
                                    styles.attendeeName
                                  }>{`${attendee?.user?.first_name} ${attendee?.user?.last_name}`}</Text>
                              ) : (
                                <Text style={[styles.attendeeName, {marginLeft: 0}]}>
                                  {`${attendee?.name}`} <Text style={styles.attendeeSub}>{'(external)'}</Text>
                                </Text>
                              )}
                            </View>
                            {isUserEvent && (
                              <Button
                                label={t('generic.remove')}
                                onPress={async () => {
                                  await deleteEventUserStatus({
                                    event_id: event.event_id,
                                    user_id: attendee.user?.uid,
                                    email: attendee.email,
                                  });
                                }}
                                containerStyle={styles.buttonRejectContainer}
                                textStyle={styles.buttonText}
                              />
                            )}
                          </TouchableOpacity>
                          <View style={styles.divider} />
                        </View>
                      ))
                    ) : (
                      <SkeletonPlaceholder>
                        <SkeletonPlaceholder.Item width={'100%'}>
                          <SkeletonPlaceholder.Item width={'100%'} height={45} borderRadius={8} marginBottom={8} />
                          <SkeletonPlaceholder.Item width={'100%'} height={45} borderRadius={8} marginBottom={8} />
                          <SkeletonPlaceholder.Item width={'100%'} height={45} borderRadius={8} marginBottom={8} />
                        </SkeletonPlaceholder.Item>
                      </SkeletonPlaceholder>
                    )}
                  </>
                )}
              </View>
            </View>
          </Animated.View>

          {/* Bottom Spacing for Scroll */}
          <View style={{height: 100}} />
        </Animated.ScrollView>

        {/* COMMENT SHEET MODAL */}
        <CommentSheet
          ref={commentSheetRef}
          commentList={commentList || []}
          onAddComment={onAddComment}
          isLoading={isCommentEventLoading}
          isUserEvent={isUserEvent}
        />

        {/* POSTAL CODE DIALOG FOR GREEK EVENTS */}
        <Dialog
          visible={pincodeDialogVisible}
          contentStyle={{padding: 0}}
          contentInsetAdjustmentBehavior="always"
          titleStyle={{
            alignSelf: 'flex-start',
            fontSize: 14,
            fontWeight: 'bold',
          }}
          title="Please enter post code"
          onTouchOutside={() => setPinCodeDialog(false)}
          onRequestClose={() => setPinCodeDialog(false)}>
          <View
            style={{
              paddingHorizontal: 15,
              paddingBottom: 15,
            }}>
            <TextInput
              placeholder="Enter post code"
              value={postCode}
              style={styles.textInput}
              onChangeText={setPostCode}
            />
            <Button
              label={'Continue'}
              containerStyle={{backgroundColor: colors.primary, marginTop: 10}}
              textStyle={{color: colors.white}}
              onPress={onContinuePress}
            />
          </View>
        </Dialog>

        {/* ISSUE REPORTING MODAL */}
        <IssueModal
          visible={modalVisible}
          onClose={() => setModalVisible(false)}
          onTechnicalIssueClick={() => {
            console.log('Technical Issue Selected');
            onIssueTypeClick('t');
            setModalVisible(false);
          }}
          onEventIssueClick={() => {
            onIssueTypeClick('e');
            console.log('Event Issue Selected');
            setModalVisible(false);
          }}
        />

        {/* MATCHING OPTIONS SHEET */}
        <BottomSheetModal
          ref={matchingOptionsSheetRef}
          snapPoints={['40%']}
          enablePanDownToClose
          backdropComponent={() => (
            <View style={{...StyleSheet.absoluteFillObject, backgroundColor: 'rgba(0,0,0,0.5)'}} />
          )}>
          <View style={styles.matchingOptionsContainer}>
            <Text style={styles.matchingOptionsTitle}>Find people to go with</Text>
            <TouchableOpacity
              style={styles.matchingOption}
              onPress={() => {
                setCurrentMatchingEvent(event?.event_id || null);
                setDomain(userDomain || null);
                setMatchingType('anyone');
                setRefresh(false);
                matchingOptionsSheetRef.current?.dismiss();
                openMatchingLoadingModal();
              }}>
              <Text style={styles.matchingOptionText}>Anyone</Text>
            </TouchableOpacity>
            {groups && groups.length > 0 && (
              <TouchableOpacity
                style={styles.matchingOption}
                onPress={() => {
                  setCurrentMatchingEvent(event?.event_id || null);
                  setDomain(null);
                  setMatchingType('neighbours');
                  setRefresh(false);
                  matchingOptionsSheetRef.current?.dismiss();
                  openMatchingLoadingModal();
                }}>
                <Text style={styles.matchingOptionText}>My Neighbours</Text>
              </TouchableOpacity>
            )}
          </View>
        </BottomSheetModal>

        {/* ANIMATED LOADING MODAL FOR QR CODE */}
        <AnimatedLoadingModal
          isVisible={isAnimationVisible}
          isButtonVisible={false}
          callback={() => setAnimationVisisble(false)}
          close={() => setAnimationVisisble(false)}
          isQRCode={true}
        />

        {/* CALENDAR MODAL FOR RECURRING EVENTS */}
        <Calendar ref={calendarRef} recurrences={event?.recurrence_events || []} onJoinEvent={joinEvent} />

        {/* Join Button - Updated with new logic based on original EventDetails.tsx */}
        {event?.host_id !== auth().currentUser?.uid && !userStatusIsLoading && !userStatusIsFetching && (
          <Animated.View style={styles.joinButtonContainer} entering={FadeInUp.delay(800)}>
            {userStatus?.status === USER_EVENT_STATUS.ACCEPTED ? (
              // User has joined - show "Find someone to go with" button
              <TouchableOpacity
                style={[styles.joinButton, {backgroundColor: colors.statusPurple}]}
                onPress={handleMatchingPress}
                activeOpacity={0.9}>
                <Text style={styles.joinButtonText}>
                  {matchingStatus?.matches_available
                    ? matchingStatus?.type === 'all'
                      ? t('events.see_my_match')
                      : t('home.find_someone')
                    : t('home.find_someone')}
                </Text>
              </TouchableOpacity>
            ) : (
              // User hasn't joined - show join/buy button
              <TouchableOpacity
                style={[styles.joinButton, {backgroundColor: getEventTypeColor()}]}
                onPress={onJoinEventClick}
                activeOpacity={0.9}
                disabled={
                  userStatus?.status === USER_EVENT_STATUS.PENDING ||
                  event?.is_cancelled ||
                  moment(event?.end_date).isBefore(moment())
                }>
                <Text style={styles.joinButtonText}>
                  {userStatus?.status === USER_EVENT_STATUS.PENDING
                    ? 'Pending'
                    : userStatus?.status === 'waiting'
                      ? 'Waitlisted'
                      : event?.is_paid || event?.payment_url
                        ? 'Buy tickets'
                        : 'Join'}
                </Text>
              </TouchableOpacity>
            )}
          </Animated.View>
        )}
      </View>
    </BottomSheetModalProvider>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    headerContainer: {
      height: HEADER_HEIGHT, // Use the reduced height
      position: 'relative',
    },
    headerImage: {
      width: '100%',
      height: '100%',
    },
    headerOverlay: {
      ...StyleSheet.absoluteFillObject,
      backgroundColor: 'rgba(0, 0, 0, 0.3)',
    },
    headerActions: {
      position: 'absolute',
      left: spacing.md,
      right: spacing.md,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      zIndex: 100, // High z-index to ensure clickability
    },
    rightActions: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: spacing.sm,
    },
    actionButton: {
      width: 44,
      height: 44,
      borderRadius: 22,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      alignItems: 'center',
      justifyContent: 'center',
    },
    eventTypeBadge: {
      position: 'absolute',
      bottom: spacing.lg,
      left: spacing.md,
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      borderRadius: borderRadius.full,
    },
    eventTypeBadgeText: {
      color: colors.white,
      fontSize: typography.fontSize.xs,
      fontWeight: typography.fontWeight.bold,
      textTransform: 'uppercase',
      letterSpacing: 0.5,
    },
    stickyHeader: {
      position: 'absolute',
      left: 0,
      right: 0,
      height: 60,
      backgroundColor: colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: spacing.lg,
      zIndex: 50, // Below header actions (100) but above content
    },
    stickyBackButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: 'rgba(0, 0, 0, 0.1)',
      alignItems: 'center',
      justifyContent: 'center',
    },
    stickyTitle: {
      flex: 1,
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.bold,
      color: colors.textPrimary,
      textAlign: 'center',
      marginHorizontal: spacing.md,
    },
    stickyPlaceholder: {
      width: 40,
      height: 40,
    },
    bottomRightActions: {
      position: 'absolute',
      bottom: spacing.lg,
      right: spacing.lg,
      flexDirection: 'row',
      alignItems: 'center',
      gap: spacing.sm,
      zIndex: 10,
    },
    scrollView: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },
    scrollContent: {
      paddingBottom: spacing.xl, // Just bottom padding for content
    },
    headerSpacer: {
      height: HEADER_HEIGHT - borderRadius.xl, // Space for header minus overlap
      position: 'relative',
    },
    scrollableBottomRightActions: {
      position: 'absolute',
      bottom: spacing.lg,
      right: spacing.lg,
      flexDirection: 'row',
      alignItems: 'center',
      gap: spacing.sm,
      zIndex: 10,
    },
    contentCard: {
      backgroundColor: colors.surface,
      borderTopLeftRadius: borderRadius.xl,
      borderTopRightRadius: borderRadius.xl,
      paddingHorizontal: spacing.lg,
      paddingTop: spacing.lg,
      minHeight: SCREEN_HEIGHT,
    },
    eventTitle: {
      fontSize: typography.fontSize['3xl'],
      fontWeight: typography.fontWeight.bold,
      color: colors.textPrimary,
      lineHeight: typography.fontSize['3xl'] * 1.2,
      marginBottom: spacing.md,
    },
    hostSection: {
      marginBottom: spacing.xl,
    },
    hostLabel: {
      fontSize: typography.fontSize.sm,
      color: colors.textSecondary,
      marginBottom: spacing.xs,
    },
    hostName: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.semibold,
      color: colors.textPrimary,
    },
    infoSection: {
      marginBottom: spacing.lg,
    },
    infoRow: {
      flexDirection: 'row',
      alignItems: 'flex-start',
    },
    iconContainer: {
      width: 48,
      height: 48,
      borderRadius: 24,
      backgroundColor: colors.gray100,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: spacing.md,
    },
    infoContent: {
      flex: 1,
      paddingTop: spacing.xs,
    },
    infoTitle: {
      fontSize: typography.fontSize.base,
      fontWeight: typography.fontWeight.semibold,
      color: colors.textPrimary,
      marginBottom: spacing.xs,
    },
    infoText: {
      fontSize: typography.fontSize.base,
      color: colors.textPrimary,
      lineHeight: typography.fontSize.base * 1.4,
    },
    infoSubtext: {
      fontSize: typography.fontSize.sm,
      color: colors.textSecondary,
      marginTop: spacing.xs,
    },
    directionsButton: {
      marginTop: spacing.sm,
    },
    directionsText: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.medium,
    },
    descriptionSection: {
      marginTop: spacing.xl,
      marginBottom: spacing.xl,
    },
    sectionTitle: {
      fontSize: typography.fontSize.xl,
      fontWeight: typography.fontWeight.bold,
      color: colors.textPrimary,
      marginBottom: spacing.md,
    },
    descriptionText: {
      fontSize: typography.fontSize.base,
      color: colors.textPrimary,
      lineHeight: typography.fontSize.base * 1.5,
    },
    expandButton: {
      marginTop: spacing.sm,
    },
    expandText: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.medium,
    },
    attendeesContainer: {
      marginTop: spacing.md,
      flexDirection: 'row',
      alignItems: 'center',
    },
    attendeesAvatars: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    attendeeAvatar: {
      width: 32,
      height: 32,
      borderRadius: 16,
      borderWidth: 2,
      borderColor: colors.white,
    },
    attendeeImage: {
      width: '100%',
      height: '100%',
      borderRadius: 14,
    },
    attendeePlaceholder: {
      backgroundColor: colors.gray300,
      alignItems: 'center',
      justifyContent: 'center',
    },
    attendeeInitial: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.bold,
      color: colors.white,
    },
    moreAttendeesContainer: {
      backgroundColor: colors.gray400,
      alignItems: 'center',
      justifyContent: 'center',
    },
    moreAttendeesText: {
      fontSize: typography.fontSize.xs,
      fontWeight: typography.fontWeight.bold,
      color: colors.white,
    },
    joinButtonContainer: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      backgroundColor: colors.surface,
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },
    joinButton: {
      height: 56,
      borderRadius: borderRadius.lg,
      alignItems: 'center',
      justifyContent: 'center',
    },
    joinButtonText: {
      color: colors.white,
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.bold,
    },
    eventInfoGrid: {
      marginBottom: spacing.xl,
    },
    infoItem: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border + '20',
    },
    calendarIconButton: {
      padding: spacing.sm,
      borderRadius: borderRadius.md,
      backgroundColor: colors.primary + '10',
      alignItems: 'center',
      justifyContent: 'center',
    },
    infoIcon: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: colors.primary + '15',
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: spacing.md,
    },
    infoContentOld: {
      flex: 1,
    },
    infoLabel: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.medium,
      color: colors.textSecondary,
      marginBottom: spacing.xs,
      textTransform: 'uppercase',
      letterSpacing: 0.5,
    },
    infoValue: {
      fontSize: typography.fontSize.base,
      fontWeight: typography.fontWeight.semibold,
      color: colors.text,
      marginBottom: spacing.xs,
    },
    infoSubValue: {
      fontSize: typography.fontSize.sm,
      color: colors.textSecondary,
    },
    attendeeAvatarsRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: spacing.xs,
    },
    attendeeAvatarSmall: {
      width: 28,
      height: 28,
      borderRadius: 14,
      borderWidth: 2,
      borderColor: colors.surface,
      overflow: 'hidden',
    },
    attendeeAvatarImage: {
      width: '100%',
      height: '100%',
      borderRadius: 12,
    },
    attendeePlaceholderOld: {
      backgroundColor: colors.primary,
      alignItems: 'center',
      justifyContent: 'center',
    },
    attendeeInitialOld: {
      fontSize: typography.fontSize.xs,
      fontWeight: typography.fontWeight.bold,
      color: colors.white,
    },
    moreAttendeesTextOld: {
      fontSize: typography.fontSize.xs,
      color: colors.textSecondary,
      marginLeft: spacing.sm,
      fontWeight: typography.fontWeight.medium,
    },
    descriptionSectionOld: {
      marginBottom: spacing.xl,
      paddingTop: spacing.lg,
      borderTopWidth: 1,
      borderTopColor: colors.border + '30',
    },
    sectionTitleOld: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.bold,
      color: colors.text,
      marginBottom: spacing.md,
    },
    eventDescription: {
      fontSize: typography.fontSize.base,
      color: colors.textSecondary,
      lineHeight: 24,
    },
    expandButtonOld: {
      marginTop: spacing.sm,
    },
    expandTextOld: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.medium,
    },
    joinSection: {
      marginTop: spacing.xl,
      paddingTop: spacing.lg,
      borderTopWidth: 1,
      borderTopColor: colors.border + '30',
    },
    loaderJoinBtnView: {
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: spacing.lg,
      height: 50,
    },
    findPeopleSection: {
      marginTop: spacing.lg,
      paddingTop: spacing.lg,
      borderTopWidth: 1,
      borderTopColor: colors.border + '30',
    },
    hostSectionOld: {
      marginTop: spacing.xl,
      paddingTop: spacing.lg,
      borderTopWidth: 1,
      borderTopColor: colors.border + '30',
    },
    sectionHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: spacing.md,
    },
    hostContainer: {
      backgroundColor: colors.surfaceVariant + '50',
      borderRadius: borderRadius.lg,
      padding: spacing.md,
      marginTop: spacing.sm,
    },
    hostInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: spacing.md,
    },
    hostPhoto: {
      width: 56,
      height: 56,
      borderRadius: 28,
      overflow: 'hidden',
      borderWidth: 2,
      borderColor: colors.primary + '20',
    },
    hostNameOld: {
      flex: 1,
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.semibold,
      color: colors.text,
    },
    attendeesSection: {
      marginTop: spacing.xl,
      paddingTop: spacing.lg,
      borderTopWidth: 1,
      borderTopColor: colors.border + '30',
    },
    attendeesContainerOld: {
      marginTop: spacing.md,
    },
    attendeeRowStyle: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: spacing.md,
      backgroundColor: colors.surfaceVariant + '30',
      padding: spacing.md,
      borderRadius: borderRadius.lg,
    },
    profileImg: {
      width: 40,
      height: 40,
      borderRadius: 20,
      borderWidth: 2,
      borderColor: colors.surface,
    },
    attendeesCount: {
      fontSize: typography.fontSize.sm,
      color: colors.textSecondary,
      marginLeft: spacing.sm,
      fontWeight: typography.fontWeight.medium,
    },
    attendeeContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: spacing.md,
      backgroundColor: colors.surfaceVariant + '20',
      borderRadius: borderRadius.md,
      paddingHorizontal: spacing.md,
      marginBottom: spacing.sm,
    },
    attendeeLeftContainer: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      gap: spacing.md,
    },
    attendeePhoto: {
      width: 48,
      height: 48,
      borderRadius: 24,
      overflow: 'hidden',
      borderWidth: 2,
      borderColor: colors.primary + '20',
    },
    attendeeName: {
      fontSize: typography.fontSize.base,
      fontWeight: typography.fontWeight.semibold,
      color: colors.text,
    },
    attendeeSub: {
      fontSize: typography.fontSize.sm,
      color: colors.textSecondary,
      fontStyle: 'italic',
    },
    buttonRejectContainer: {
      backgroundColor: colors.error,
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      borderRadius: borderRadius.lg,
    },
    buttonText: {
      fontSize: typography.fontSize.sm,
      color: colors.white,
      fontWeight: typography.fontWeight.semibold,
    },
    divider: {
      height: 1,
      backgroundColor: colors.border + '30',
      marginVertical: spacing.xs,
    },
    commentsSection: {
      marginTop: spacing.xl,
      paddingTop: spacing.lg,
      borderTopWidth: 1,
      borderTopColor: colors.border + '30',
    },
    commentView: {
      backgroundColor: colors.surfaceVariant + '40',
      borderRadius: borderRadius.xl,
      padding: spacing.lg,
      borderWidth: 1,
      borderColor: colors.border + '20',
    },
    commentTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.bold,
      color: colors.text,
      marginBottom: spacing.md,
    },
    commentItemView: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      gap: spacing.md,
    },
    userImage: {
      width: 44,
      height: 44,
      borderRadius: 22,
      borderWidth: 2,
      borderColor: colors.primary + '20',
    },
    commentTextView: {
      flex: 1,
      fontSize: typography.fontSize.base,
      color: colors.textSecondary,
      lineHeight: 22,
    },
    eventManagementSection: {
      marginTop: spacing.xl,
      paddingTop: spacing.lg,
      borderTopWidth: 1,
      borderTopColor: colors.border + '30',
    },
    errorText: {
      fontSize: typography.fontSize.base,
      color: colors.error,
      textAlign: 'center',
      marginBottom: spacing.md,
      fontWeight: typography.fontWeight.semibold,
      backgroundColor: colors.error + '10',
      padding: spacing.md,
      borderRadius: borderRadius.md,
    },
    textInput: {
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: borderRadius.lg,
      padding: spacing.md,
      fontSize: typography.fontSize.base,
      color: colors.text,
      backgroundColor: colors.surface,
    },
    matchingOptionsContainer: {
      padding: spacing.xl,
    },
    matchingOptionsTitle: {
      fontSize: typography.fontSize.xl,
      fontWeight: typography.fontWeight.bold,
      color: colors.text,
      marginBottom: spacing.xl,
      textAlign: 'center',
    },
    matchingOption: {
      backgroundColor: colors.primary,
      padding: spacing.lg,
      borderRadius: borderRadius.xl,
      marginBottom: spacing.md,
      alignItems: 'center',
      shadowColor: colors.black,
      shadowOffset: {width: 0, height: 2},
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    matchingOptionText: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.semibold,
      color: colors.white,
    },
  });

export default CompleteModernEventDetail;
